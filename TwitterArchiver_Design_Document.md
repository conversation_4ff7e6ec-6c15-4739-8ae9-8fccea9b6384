# TwitterArchiver 设计文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [核心模块设计](#核心模块设计)
- [反爬虫策略](#反爬虫策略)
- [文件组织结构](#文件组织结构)
- [配置系统](#配置系统)
- [使用示例](#使用示例)
- [部署指南](#部署指南)
- [法律合规](#法律合规)

## 🎯 项目概述

### 项目目标
TwitterArchiver 是一个专业的Twitter内容备份工具，旨在帮助用户安全、高效地备份Twitter账户的所有内容，包括推文、图片、视频和元数据。

### 核心特性
- ✅ **全面备份** - 推文文本、图片、视频、元数据
- ✅ **智能反检测** - 多层反爬虫策略
- ✅ **增量更新** - 只下载新内容
- ✅ **高度可配置** - 灵活的配置选项
- ✅ **用户友好** - 清晰的进度显示和错误处理
- ✅ **合规设计** - 遵守法律和平台规则

### 技术亮点
- 多种认证方式支持
- 智能速率控制
- 真实浏览器模拟
- 人类行为仿真
- 异常自动恢复
- 分布式请求策略

## 🏗️ 技术架构

### 技术栈
```
核心语言: Python 3.9+
网络请求: aiohttp, requests
浏览器自动化: Playwright
数据库: SQLite3 / PostgreSQL
配置管理: PyYAML
命令行界面: Click
异步处理: asyncio
```

### 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层    │    │   配置管理层    │    │   日志监控层    │
│  CLI / Web UI   │    │  YAML Config    │    │  Logging/Stats  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                        核心控制层                                │
│              TwitterArchiver Main Controller                    │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                           │                           │
┌───▼────┐  ┌────▼────┐  ┌──────▼──────┐  ┌────▼────┐  ┌───▼────┐
│认证模块│  │爬取模块 │  │反检测模块   │  │存储模块 │  │导出模块│
│Auth    │  │Scraper  │  │AntiDetection│  │Storage  │  │Export  │
└────────┘  └─────────┘  └─────────────┘  └─────────┘  └────────┘
```

### 模块职责
- **认证模块**: 处理多种登录方式，维护会话状态
- **爬取模块**: 数据获取、分页处理、并发控制
- **反检测模块**: 反爬虫策略、行为模拟、异常处理
- **存储模块**: 文件管理、数据库操作、去重机制
- **导出模块**: 多格式导出、数据可视化、统计报告

## 🔧 核心模块设计

### 1. 认证模块 (TwitterAuth)
```python
class TwitterAuth:
    """Twitter认证管理器"""
    
    def __init__(self, config):
        self.config = config
        self.session = None
        self.cookies = None
        self.auth_method = config.get('auth_method', 'cookies')
    
    async def authenticate(self):
        """执行认证流程"""
        if self.auth_method == 'cookies':
            return await self._auth_with_cookies()
        elif self.auth_method == 'credentials':
            return await self._auth_with_credentials()
        elif self.auth_method == 'api':
            return await self._auth_with_api()
    
    async def _auth_with_cookies(self):
        """使用Cookies认证"""
        cookies_file = self.config.get('cookies_file')
        if cookies_file:
            self.cookies = self._load_cookies(cookies_file)
        else:
            self.cookies = self._extract_browser_cookies()
        return self._validate_cookies()
    
    async def refresh_session(self):
        """自动刷新会话"""
        # 检测会话是否过期
        # 自动重新认证
        pass
```

### 2. 数据获取模块 (TwitterScraper)
```python
class TwitterScraper:
    """Twitter数据爬取器"""
    
    def __init__(self, auth, anti_detection):
        self.auth = auth
        self.anti_detection = anti_detection
        self.rate_limiter = RateLimiter()
        
    async def get_user_tweets(self, username, options=None):
        """获取用户推文"""
        tweets = []
        cursor = None
        
        while True:
            # 应用反检测策略
            await self.anti_detection.prepare_request()
            
            # 速率限制
            await self.rate_limiter.wait_if_needed()
            
            # 获取一页数据
            page_data = await self._fetch_tweets_page(username, cursor)
            
            if not page_data or not page_data.get('tweets'):
                break
                
            tweets.extend(page_data['tweets'])
            cursor = page_data.get('next_cursor')
            
            # 检查是否达到限制
            if options and len(tweets) >= options.get('max_tweets', float('inf')):
                break
                
        return tweets
    
    async def download_media(self, media_urls, tweet_id):
        """下载媒体文件"""
        downloaded_files = []
        
        for i, url in enumerate(media_urls):
            filename = f"{tweet_id}_{i+1}.{self._get_extension(url)}"
            file_path = await self._download_file(url, filename)
            downloaded_files.append(file_path)
            
        return downloaded_files
```

### 3. 反检测模块 (AntiDetectionManager)
```python
class AntiDetectionManager:
    """反检测策略管理器"""
    
    def __init__(self, config):
        self.config = config
        self.browser_stealth = BrowserStealth()
        self.behavior_sim = HumanBehaviorSimulator()
        self.rate_limiter = AdaptiveRateLimiter()
        
    async def prepare_request(self):
        """请求前的准备工作"""
        # 速率控制
        await self.rate_limiter.adaptive_wait()
        
        # 行为模拟
        if random.random() < 0.1:  # 10%概率进行随机行为
            await self.behavior_sim.random_action()
    
    async def handle_detection(self, response):
        """处理检测到的异常"""
        if self._is_rate_limited(response):
            await self._handle_rate_limit()
        elif self._is_captcha(response):
            await self._handle_captcha()
        elif self._is_blocked(response):
            await self._handle_block()
    
    async def _handle_rate_limit(self):
        """处理速率限制"""
        wait_time = random.uniform(300, 600)  # 5-10分钟
        print(f"检测到速率限制，等待 {wait_time/60:.1f} 分钟...")
        await asyncio.sleep(wait_time)
```

## 🛡️ 反爬虫策略

### 分层防护体系

#### 第一层：智能速率控制
```python
class AdaptiveRateLimiter:
    """自适应速率限制器"""
    
    def __init__(self):
        self.success_rate = 1.0
        self.base_delay = 2.0
        self.current_delay = 2.0
        self.request_history = deque(maxlen=100)
        
    async def adaptive_wait(self):
        """根据成功率动态调整延迟"""
        # 成功率低时增加延迟
        if self.success_rate < 0.8:
            self.current_delay = min(self.current_delay * 1.5, 30.0)
        # 成功率高时适当减少延迟
        elif self.success_rate > 0.95:
            self.current_delay = max(self.current_delay * 0.9, 1.0)
            
        # 添加随机抖动
        jitter = random.uniform(0.5, 1.5)
        await asyncio.sleep(self.current_delay * jitter)
```

#### 第二层：浏览器环境伪装
```python
class BrowserStealth:
    """浏览器隐蔽模式"""
    
    async def create_stealth_context(self):
        """创建隐蔽的浏览器环境"""
        browser = await playwright.chromium.launch(
            headless=False,  # 非headless模式更难检测
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--disable-default-apps'
            ]
        )
        
        context = await browser.new_context(
            user_agent=self._get_random_user_agent(),
            viewport={'width': 1920, 'height': 1080},
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # 注入反检测脚本
        await context.add_init_script(self._get_stealth_script())
        return context
```

#### 第三层：人类行为模拟
```python
class HumanBehaviorSimulator:
    """人类行为模拟器"""
    
    async def simulate_reading(self, duration_range=(2, 8)):
        """模拟阅读停留"""
        wait_time = random.uniform(*duration_range)
        await asyncio.sleep(wait_time)
    
    async def natural_scroll(self, page):
        """自然滚动行为"""
        scroll_distance = random.randint(300, 800)
        scroll_steps = random.randint(3, 8)
        
        for _ in range(scroll_steps):
            step_distance = scroll_distance // scroll_steps
            await page.mouse.wheel(0, step_distance)
            await asyncio.sleep(random.uniform(0.1, 0.3))
```

#### 第四层：多重备用策略
```python
class MultiLayerStrategy:
    """多层策略管理器"""
    
    def __init__(self):
        self.strategies = [
            self._official_api_strategy,      # 最安全
            self._browser_automation_strategy, # 中等风险
            self._mobile_browser_strategy,    # 备选方案
            self._api_reverse_strategy        # 最后手段
        ]
        
    async def execute_with_fallback(self, task, *args, **kwargs):
        """多策略执行，自动降级"""
        for i, strategy in enumerate(self.strategies):
            try:
                result = await strategy(task, *args, **kwargs)
                if self._validate_result(result):
                    return result
            except Exception as e:
                if i < len(self.strategies) - 1:
                    await asyncio.sleep(random.uniform(30, 60))
                    continue
                else:
                    raise Exception("所有策略都失败了")
```

## 📁 文件组织结构

### 项目目录结构
```
TwitterArchiver/
├── src/
│   ├── __init__.py
│   ├── main.py                 # 主程序入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── config_manager.py   # 配置管理
│   │   └── default_config.yaml # 默认配置
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── twitter_auth.py     # Twitter认证
│   │   └── session_manager.py  # 会话管理
│   ├── scraper/
│   │   ├── __init__.py
│   │   ├── twitter_scraper.py  # 数据爬取
│   │   ├── media_downloader.py # 媒体下载
│   │   └── rate_limiter.py     # 速率控制
│   ├── anti_detection/
│   │   ├── __init__.py
│   │   ├── stealth_browser.py  # 浏览器伪装
│   │   ├── behavior_sim.py     # 行为模拟
│   │   └── detection_handler.py # 检测处理
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── file_manager.py     # 文件管理
│   │   ├── database.py         # 数据库操作
│   │   └── archive_manager.py  # 归档管理
│   ├── export/
│   │   ├── __init__.py
│   │   ├── json_exporter.py    # JSON导出
│   │   ├── html_exporter.py    # HTML导出
│   │   └── csv_exporter.py     # CSV导出
│   └── utils/
│       ├── __init__.py
│       ├── logger.py           # 日志系统
│       ├── progress.py         # 进度显示
│       └── validators.py       # 数据验证
├── backups/                    # 备份数据目录
├── config/
│   └── config.yaml            # 用户配置文件
├── logs/                      # 日志文件
├── tests/                     # 测试文件
├── docs/                      # 文档
├── requirements.txt           # 依赖列表
├── setup.py                   # 安装脚本
└── README.md                  # 项目说明
```

### 备份数据结构
```
backups/
└── CycleStudies/              # 用户名目录
    ├── tweets/                # 推文数据
    │   ├── 2024-01/
    │   │   ├── 1234567890.json
    │   │   └── 1234567891.json
    │   └── 2024-02/
    ├── media/                 # 媒体文件
    │   ├── images/
    │   │   ├── 1234567890_1.jpg
    │   │   └── 1234567890_2.png
    │   └── videos/
    │       └── 1234567890_video.mp4
    ├── profile/               # 用户资料
    │   ├── profile.json
    │   └── avatar.jpg
    ├── metadata/              # 元数据
    │   ├── backup_info.json
    │   └── statistics.json
    ├── archive.db             # 本地数据库
    └── backup.log             # 备份日志
```

## ⚙️ 配置系统

### 配置文件示例 (config.yaml)
```yaml
# TwitterArchiver 配置文件
twitter:
  # 认证配置
  auth:
    method: "cookies"           # cookies, credentials, api
    cookies_file: "cookies.txt" # Cookies文件路径
    cookies_browser: "firefox"  # 浏览器类型
    username: ""                # 用户名（credentials方式）
    password: ""                # 密码（credentials方式）
    api_key: ""                 # API密钥（api方式）
    api_secret: ""              # API密钥（api方式）

  # 请求限制
  limits:
    requests_per_minute: 30     # 每分钟请求数
    concurrent_downloads: 5     # 并发下载数
    retry_attempts: 3           # 重试次数
    timeout: 30                 # 请求超时（秒）

  # 反检测配置
  anti_detection:
    enable_stealth: true        # 启用隐蔽模式
    simulate_human: true        # 模拟人类行为
    random_delays: true         # 随机延迟
    use_proxy: false            # 使用代理
    proxy_list: []              # 代理列表

backup:
  # 基础配置
  base_directory: "./backups"   # 备份根目录
  organize_by_date: true        # 按日期组织
  include_media: true           # 包含媒体文件
  include_retweets: false       # 包含转推
  include_replies: true         # 包含回复

  # 时间范围
  date_range:
    start: null                 # 开始日期 (YYYY-MM-DD)
    end: null                   # 结束日期 (YYYY-MM-DD)

  # 媒体配置
  media:
    max_file_size: "100MB"      # 最大文件大小
    formats: ["jpg", "png", "gif", "mp4", "webm"]  # 支持格式
    quality: "original"         # 质量设置

  # 元数据配置
  metadata:
    save_full_json: true        # 保存完整JSON
    include_analytics: false    # 包含分析数据
    include_user_info: true     # 包含用户信息

# 输出配置
output:
  # 文件命名
  filename_template: "{tweet_id}_{timestamp}"  # 文件名模板
  date_format: "%Y-%m-%d"       # 日期格式

  # 日志配置
  log_level: "INFO"             # 日志级别
  log_file: "logs/backup.log"   # 日志文件

  # 进度显示
  show_progress: true           # 显示进度条
  progress_update_interval: 10  # 进度更新间隔（秒）

# 数据库配置
database:
  type: "sqlite"                # 数据库类型
  path: "archive.db"            # 数据库路径
  backup_enabled: true          # 启用数据库备份

# 导出配置
export:
  formats: ["json", "html"]     # 导出格式
  include_statistics: true      # 包含统计信息
  generate_index: true          # 生成索引页面
```

### 命令行参数
```bash
# 基础使用
twitter-archiver backup @CycleStudies

# 完整参数示例
twitter-archiver backup @CycleStudies \
    --config config.yaml \
    --since 2024-01-01 \
    --until 2024-12-31 \
    --include-media \
    --exclude-retweets \
    --max-tweets 5000 \
    --output-dir ./custom_backup \
    --log-level DEBUG \
    --concurrent 3

# 增量备份
twitter-archiver sync @CycleStudies

# 导出数据
twitter-archiver export @CycleStudies \
    --format html \
    --output backup_report.html

# 实时监控
twitter-archiver monitor @CycleStudies \
    --interval 30m \
    --notify-email <EMAIL>

# 配置管理
twitter-archiver config init          # 初始化配置
twitter-archiver config validate      # 验证配置
twitter-archiver config show          # 显示当前配置
```

## 💻 使用示例

### 1. 基础备份流程
```python
import asyncio
from twitter_archiver import TwitterArchiver

async def basic_backup():
    # 初始化
    archiver = TwitterArchiver(config_file="config.yaml")

    # 认证
    await archiver.authenticate()

    # 备份用户
    result = await archiver.backup_user(
        username="CycleStudies",
        include_media=True,
        max_tweets=1000
    )

    print(f"备份完成: {result.total_tweets} 条推文, {result.total_media} 个媒体文件")

# 运行
asyncio.run(basic_backup())
```

### 2. 高级配置示例
```python
async def advanced_backup():
    config = {
        'twitter': {
            'auth': {
                'method': 'cookies',
                'cookies_browser': 'firefox'
            },
            'limits': {
                'requests_per_minute': 20,
                'concurrent_downloads': 3
            },
            'anti_detection': {
                'enable_stealth': True,
                'simulate_human': True
            }
        },
        'backup': {
            'include_media': True,
            'include_retweets': False,
            'date_range': {
                'start': '2024-01-01',
                'end': '2024-12-31'
            }
        }
    }

    archiver = TwitterArchiver(config=config)

    # 设置进度回调
    def progress_callback(current, total, status):
        print(f"进度: {current}/{total} ({current/total*100:.1f}%) - {status}")

    archiver.set_progress_callback(progress_callback)

    # 执行备份
    await archiver.backup_user("CycleStudies")
```

### 3. 批量备份示例
```python
async def batch_backup():
    users = ["CycleStudies", "user2", "user3"]
    archiver = TwitterArchiver()

    for user in users:
        try:
            print(f"开始备份用户: {user}")
            await archiver.backup_user(user)
            print(f"用户 {user} 备份完成")

            # 用户间等待，避免触发限制
            await asyncio.sleep(300)  # 5分钟

        except Exception as e:
            print(f"用户 {user} 备份失败: {e}")
            continue
```

## 🚀 部署指南

### 环境要求
```
Python: 3.9+
内存: 最少 2GB，推荐 4GB+
存储: 根据备份内容，建议预留充足空间
网络: 稳定的互联网连接
```

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/your-repo/TwitterArchiver.git
cd TwitterArchiver
```

#### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

#### 3. 安装依赖
```bash
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

#### 4. 初始化配置
```bash
python -m twitter_archiver config init
```

#### 5. 配置认证
```bash
# 方法1: 使用浏览器cookies
python -m twitter_archiver auth setup --method cookies --browser firefox

# 方法2: 使用用户名密码
python -m twitter_archiver auth setup --method credentials

# 方法3: 使用API密钥
python -m twitter_archiver auth setup --method api
```

### Docker部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 安装Playwright
RUN playwright install chromium

# 创建数据目录
RUN mkdir -p /app/backups /app/logs

VOLUME ["/app/backups", "/app/config", "/app/logs"]

CMD ["python", "-m", "twitter_archiver"]
```

```bash
# 构建和运行
docker build -t twitter-archiver .

docker run -d \
    --name twitter-archiver \
    -v $(pwd)/backups:/app/backups \
    -v $(pwd)/config:/app/config \
    -v $(pwd)/logs:/app/logs \
    twitter-archiver
```

## ⚖️ 法律合规

### 使用条款
1. **个人使用** - 仅限个人学习、研究和备份用途
2. **禁止商业用途** - 不得用于商业目的或盈利活动
3. **尊重版权** - 尊重内容创作者的版权和知识产权
4. **遵守法律** - 遵守当地法律法规和Twitter服务条款

### 免责声明
```
本工具仅供学习和研究目的使用。用户在使用本工具时应：

1. 遵守Twitter的服务条款和使用政策
2. 尊重他人的隐私权和版权
3. 不得将备份的内容用于非法用途
4. 承担使用本工具可能带来的风险

开发者不对以下情况承担责任：
- 账户被封禁或限制
- 数据丢失或损坏
- 法律纠纷或争议
- 其他因使用本工具而产生的问题
```

### 最佳实践建议
1. **使用小号** - 建议使用非主要账户进行备份
2. **控制频率** - 合理控制备份频率，避免过度请求
3. **定期更新** - 保持工具版本更新，适应平台变化
4. **数据安全** - 妥善保管备份数据，避免泄露
5. **及时停止** - 发现异常时及时停止使用

---

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：
- GitHub Issues: [项目地址]
- 邮箱: [联系邮箱]
- 文档: [文档地址]

**注意**: 本工具仍在开发中，使用前请仔细阅读文档并测试功能。
